'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';

interface BehavioralPrinciple {
  name: string;
  explanation: string;
  videoExample: string;
}

interface VideoAnalysis {
  videoUrl?: string;
  fileName?: string;
  principles: BehavioralPrinciple[];
}

interface VideoScript {
  id: number;
  script: string;
  duration: string;
  style: string;
}

interface GeneratedContent {
  videoAnalysis?: VideoAnalysis;
  scripts: VideoScript[];
}

const VideoScriptsPage = () => {
  const [videoUrl, setVideoUrl] = useState('');
  const [uploadedVideo, setUploadedVideo] = useState<File | null>(null);
  const [productName, setProductName] = useState('');
  const [productDescription, setProductDescription] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [callToAction, setCallToAction] = useState('');
  const [videoStyle, setVideoStyle] = useState('');
  const [scriptType, setScriptType] = useState('');
  const [scriptDuration, setScriptDuration] = useState('');
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [error, setError] = useState('');
  const [rawStreamContent, setRawStreamContent] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);

  // Cache keys for localStorage
  const CACHE_KEYS = useMemo(() => ({
    videoUrl: 'videoscripts_videoUrl',
    productName: 'videoscripts_productName',
    productDescription: 'videoscripts_productDescription',
    targetAudience: 'videoscripts_targetAudience',
    callToAction: 'videoscripts_callToAction',
    videoStyle: 'videoscripts_videoStyle',
    scriptType: 'videoscripts_scriptType',
    scriptDuration: 'videoscripts_scriptDuration',
    generatedContent: 'videoscripts_generatedContent',
    uploadedVideoData: 'videoscripts_uploadedVideoData'
  }), []);

  // Load cached data on component mount
  useEffect(() => {
    const loadCachedData = () => {
      try {
        setVideoUrl(localStorage.getItem(CACHE_KEYS.videoUrl) || '');
        setProductName(localStorage.getItem(CACHE_KEYS.productName) || '');
        setProductDescription(localStorage.getItem(CACHE_KEYS.productDescription) || '');
        setTargetAudience(localStorage.getItem(CACHE_KEYS.targetAudience) || '');
        setCallToAction(localStorage.getItem(CACHE_KEYS.callToAction) || '');
        setVideoStyle(localStorage.getItem(CACHE_KEYS.videoStyle) || '');
        setScriptType(localStorage.getItem(CACHE_KEYS.scriptType) || '');
        setScriptDuration(localStorage.getItem(CACHE_KEYS.scriptDuration) || '');

        const cachedContent = localStorage.getItem(CACHE_KEYS.generatedContent);
        if (cachedContent) {
          setGeneratedContent(JSON.parse(cachedContent));
        }

        const cachedVideoData = localStorage.getItem(CACHE_KEYS.uploadedVideoData);
        if (cachedVideoData) {
          const { fileName, data } = JSON.parse(cachedVideoData);
          const byteCharacters = atob(data);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const file = new File([byteArray], fileName, { type: 'video/mp4' });
          setUploadedVideo(file);
        }
      } catch (error) {
        console.error('Error loading cached data:', error);
      }
    };

    loadCachedData();
  }, [CACHE_KEYS]);

  // Cache data whenever state changes
  useEffect(() => {
    if (videoUrl) localStorage.setItem(CACHE_KEYS.videoUrl, videoUrl);
  }, [videoUrl, CACHE_KEYS.videoUrl]);

  useEffect(() => {
    if (productName) localStorage.setItem(CACHE_KEYS.productName, productName);
  }, [productName, CACHE_KEYS.productName]);

  useEffect(() => {
    if (productDescription) localStorage.setItem(CACHE_KEYS.productDescription, productDescription);
  }, [productDescription, CACHE_KEYS.productDescription]);

  useEffect(() => {
    if (targetAudience) localStorage.setItem(CACHE_KEYS.targetAudience, targetAudience);
  }, [targetAudience, CACHE_KEYS.targetAudience]);

  useEffect(() => {
    if (callToAction) localStorage.setItem(CACHE_KEYS.callToAction, callToAction);
  }, [callToAction, CACHE_KEYS.callToAction]);

  useEffect(() => {
    if (videoStyle) localStorage.setItem(CACHE_KEYS.videoStyle, videoStyle);
  }, [videoStyle, CACHE_KEYS.videoStyle]);

  useEffect(() => {
    if (scriptType) localStorage.setItem(CACHE_KEYS.scriptType, scriptType);
  }, [scriptType, CACHE_KEYS.scriptType]);

  useEffect(() => {
    if (scriptDuration) localStorage.setItem(CACHE_KEYS.scriptDuration, scriptDuration);
  }, [scriptDuration, CACHE_KEYS.scriptDuration]);

  useEffect(() => {
    if (generatedContent) {
      localStorage.setItem(CACHE_KEYS.generatedContent, JSON.stringify(generatedContent));
    }
  }, [generatedContent, CACHE_KEYS.generatedContent]);

  const handleVideoUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('video/')) {
      setError('Please upload a valid video file.');
      return;
    }

    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      setError('Video file size must be less than 50MB.');
      return;
    }

    setUploadedVideo(file);
    setVideoUrl(''); // Clear URL if file is uploaded
    setError('');

    // Cache the uploaded video
    try {
      const arrayBuffer = await file.arrayBuffer();
      const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      localStorage.setItem(CACHE_KEYS.uploadedVideoData, JSON.stringify({
        fileName: file.name,
        data: base64
      }));
    } catch (error) {
      console.error('Error caching video:', error);
    }
  }, [CACHE_KEYS.uploadedVideoData]);

  const handleVideoUrlChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const url = event.target.value;
    setVideoUrl(url);
    if (url) {
      setUploadedVideo(null); // Clear uploaded file if URL is provided
      localStorage.removeItem(CACHE_KEYS.uploadedVideoData);
    }
  }, [CACHE_KEYS.uploadedVideoData]);

  const analyzeVideo = useCallback(async () => {
    if (!videoUrl && !uploadedVideo) {
      setError('Please provide a video URL or upload a video file.');
      return;
    }

    setAnalysisLoading(true);
    setError('');

    try {
      const formData = new FormData();
      if (uploadedVideo) {
        formData.append('video', uploadedVideo);
      } else {
        formData.append('videoUrl', videoUrl);
      }

      const response = await fetch('/api/analyze-video-science', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to analyze video');
      }

      const data = await response.json();
      
      setGeneratedContent(prev => ({
        ...prev,
        videoAnalysis: {
          videoUrl: uploadedVideo ? undefined : videoUrl,
          fileName: uploadedVideo?.name,
          principles: data.videoAnalysis.principles || []
        },
        scripts: prev?.scripts || []
      }));

    } catch (error) {
      console.error('Error analyzing video:', error);
      setError(error instanceof Error ? error.message : 'Failed to analyze video');
    } finally {
      setAnalysisLoading(false);
    }
  }, [videoUrl, uploadedVideo]);

  const generateScripts = useCallback(async () => {
    if (!productName || !productDescription || !targetAudience || !callToAction || !videoStyle || !scriptType || !scriptDuration) {
      setError('Please fill in all required fields.');
      return;
    }

    setLoading(true);
    setError('');
    setRawStreamContent('');
    setIsStreaming(true);

    try {
      const response = await fetch('/api/generate-video-scripts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoAnalysis: generatedContent?.videoAnalysis,
          productName,
          productDescription,
          targetAudience,
          callToAction,
          videoStyle,
          scriptType,
          scriptDuration
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate scripts');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let accumulatedContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        accumulatedContent += chunk;
        setRawStreamContent(accumulatedContent);

        // Try to parse scripts in real-time as content comes in
        if (accumulatedContent.includes('"scripts"') && accumulatedContent.includes('}')) {
          try {
            // Look for complete JSON structure
            const jsonStart = accumulatedContent.indexOf('{');
            const jsonEnd = accumulatedContent.lastIndexOf('}');

            if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
              const jsonString = accumulatedContent.substring(jsonStart, jsonEnd + 1);
              const scriptsData = JSON.parse(jsonString);

              if (scriptsData.scripts && Array.isArray(scriptsData.scripts) && scriptsData.scripts.length > 0) {
                console.log('Successfully parsed scripts in real-time:', scriptsData.scripts.length);
                setGeneratedContent(prev => ({
                  ...prev,
                  videoAnalysis: prev?.videoAnalysis,
                  scripts: scriptsData.scripts
                }));
              }
            }
          } catch (realtimeParseError) {
            // Continue streaming, will try to parse at the end
            console.log('Real-time parsing failed, will try at end:', realtimeParseError.message);
          }
        }
      }

      // Final parsing attempt if real-time parsing didn't work
      if (!generatedContent?.scripts || generatedContent.scripts.length === 0) {
        try {
          // Clean up the accumulated content and try to extract valid JSON
          let cleanContent = accumulatedContent.trim();

          // Remove any streaming artifacts or incomplete JSON
          cleanContent = cleanContent.replace(/^[^{]*/, ''); // Remove anything before first {
          cleanContent = cleanContent.replace(/[^}]*$/, '}'); // Ensure it ends with }

          try {
            const scriptsData = JSON.parse(cleanContent);
            if (scriptsData.scripts && Array.isArray(scriptsData.scripts)) {
              setGeneratedContent(prev => ({
                ...prev,
                videoAnalysis: prev?.videoAnalysis,
                scripts: scriptsData.scripts
              }));
            }
          } catch (directParseError) {
            console.log('Direct JSON parse failed, trying manual extraction...');

            // Try to extract scripts array manually using regex
            const scriptsMatch = accumulatedContent.match(/"scripts":\s*\[([\s\S]*?)\]/);
            if (scriptsMatch) {
              try {
                // Try to parse just the scripts array
                const scriptsArrayString = '[' + scriptsMatch[1] + ']';
                const scriptsArray = JSON.parse(scriptsArrayString);

                // Validate that each item has the expected structure
                const validScripts = scriptsArray.filter(script =>
                  script && typeof script === 'object' && script.script
                );

                if (validScripts.length > 0) {
                  setGeneratedContent(prev => ({
                    ...prev,
                    videoAnalysis: prev?.videoAnalysis,
                    scripts: validScripts
                  }));
                }
              } catch (arrayParseError) {
                console.error('Error parsing scripts array:', arrayParseError);
              }
            }
          }
        } catch (parseError) {
          console.error('Error in final parsing:', parseError);
        }
      }

    } catch (error) {
      console.error('Error generating scripts:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate scripts');
    } finally {
      setLoading(false);
      setIsStreaming(false);
    }
  }, [productName, productDescription, targetAudience, callToAction, videoStyle, scriptType, scriptDuration, generatedContent?.videoAnalysis]);

  const clearCache = useCallback(() => {
    Object.values(CACHE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
    
    setVideoUrl('');
    setUploadedVideo(null);
    setProductName('');
    setProductDescription('');
    setTargetAudience('');
    setCallToAction('');
    setVideoStyle('');
    setScriptType('');
    setScriptDuration('');
    setGeneratedContent(null);
    setError('');
    setRawStreamContent('');
  }, [CACHE_KEYS]);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold mb-2">Video Scripts</h1>
          <p className="text-gray-600">
            Generate 20 pure scripts for CapCut AI video creation. Optionally analyze a viral video for behavioral science insights, 
            then create scripts based on your product and preferred video style.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* Cache Status Indicator */}
          {(productName || videoUrl || uploadedVideo || generatedContent || videoStyle) && (
            <div className="flex items-center px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Data Cached
            </div>
          )}
          
          {/* Clear Cache Button */}
          <button
            onClick={clearCache}
            className="flex items-center px-3 py-1 bg-red-100 text-red-700 rounded-full text-xs hover:bg-red-200 transition-colors"
          >
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Clear Cache
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* Step 1: Optional Video Upload/URL */}
      <div className="mb-8">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center mb-4">
              <div className="bg-indigo-100 p-2 rounded-full mr-3">
                <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Step 1: Viral Video Analysis (Optional)</h2>
                <p className="text-gray-600 text-sm mt-1">
                  Upload a viral video or provide a URL to analyze behavioral science principles. This step is optional but can enhance script quality.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Video URL Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Video URL (TikTok, Instagram, YouTube, Twitter/X, Facebook)
                </label>
                <input
                  type="url"
                  value={videoUrl}
                  onChange={handleVideoUrlChange}
                  placeholder="https://www.tiktok.com/@user/video/123..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
              </div>

              {/* Video File Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Or Upload Video File (MP4, MOV, AVI - Max 50MB)
                </label>
                <input
                  type="file"
                  accept="video/*"
                  onChange={handleVideoUpload}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
                {uploadedVideo && (
                  <p className="text-sm text-green-600 mt-1">
                    ✓ {uploadedVideo.name} ({(uploadedVideo.size / 1024 / 1024).toFixed(1)}MB)
                  </p>
                )}
              </div>
            </div>

            {(videoUrl || uploadedVideo) && (
              <div className="mt-4">
                <button
                  onClick={analyzeVideo}
                  disabled={analysisLoading}
                  className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {analysisLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Analyzing Video...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      Analyze Video
                    </>
                  )}
                </button>
              </div>
            )}
          </div>

          {/* Video Analysis Results */}
          {generatedContent?.videoAnalysis && (
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Behavioral Science Analysis</h3>
              <div className="grid gap-4">
                {generatedContent.videoAnalysis.principles.map((principle, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">{principle.name}</h4>
                    <p className="text-gray-700 text-sm mb-2">{principle.explanation}</p>
                    <p className="text-indigo-600 text-sm">
                      <strong>Video Example:</strong> {principle.videoExample}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Step 2: Product Information and Script Configuration */}
      <div className="mb-8">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center mb-4">
              <div className="bg-green-100 p-2 rounded-full mr-3">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Step 2: Product Information & Script Configuration</h2>
                <p className="text-gray-600 text-sm mt-1">
                  Tell us about your product and specify the type of scripts you want to generate.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column - Product Information */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Product/Business Name *
                  </label>
                  <input
                    type="text"
                    value={productName}
                    onChange={(e) => setProductName(e.target.value)}
                    placeholder="e.g., FitTracker Pro"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Product Description *
                  </label>
                  <textarea
                    value={productDescription}
                    onChange={(e) => setProductDescription(e.target.value)}
                    placeholder="Describe your product, its features, and benefits..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Audience *
                  </label>
                  <input
                    type="text"
                    value={targetAudience}
                    onChange={(e) => setTargetAudience(e.target.value)}
                    placeholder="e.g., Health-conscious millennials, busy professionals"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Call to Action *
                  </label>
                  <input
                    type="text"
                    value={callToAction}
                    onChange={(e) => setCallToAction(e.target.value)}
                    placeholder="e.g., Download the app, Visit our website, Sign up for free trial"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Right Column - Script Configuration */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Video Style *
                  </label>
                  <input
                    type="text"
                    value={videoStyle}
                    onChange={(e) => setVideoStyle(e.target.value)}
                    placeholder="e.g., UGC, Personal Perspective, Explainer"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Examples: UGC (User Generated Content), Personal Perspective/POV, Explainer Video, Testimonial Style, Educational/How-to, Promotional/Sales, Storytelling/Narrative
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Script Type *
                  </label>
                  <input
                    type="text"
                    value={scriptType}
                    onChange={(e) => setScriptType(e.target.value)}
                    placeholder="e.g., Hook-focused, Problem-solution, Feature-benefit"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Examples: Hook-focused (Strong opening), Problem-solution, Feature-benefit, Social proof, Curiosity-driven, Emotional appeal, Comparison/Before-after
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Script Duration *
                  </label>
                  <input
                    type="text"
                    value={scriptDuration}
                    onChange={(e) => setScriptDuration(e.target.value)}
                    placeholder="e.g., 30 seconds, 1 minute, 90 seconds"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Examples: 15 seconds, 30 seconds, 60 seconds, 90 seconds, 2 minutes
                  </p>
                </div>

                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-900 mb-2">Complete Example:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• UGC style, Hook-focused, 30-second scripts about fitness tracking</li>
                    <li>• Personal perspective, Problem-solution, 60-second scripts on productivity</li>
                    <li>• Explainer style, Feature-benefit, 15-second scripts for app features</li>
                    <li>• Testimonial style, Social proof, 45-second scripts about "Top 5 fruits diabetics should avoid"</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={generateScripts}
                disabled={loading || !productName || !productDescription || !targetAudience || !callToAction || !videoStyle || !scriptType || !scriptDuration}
                className="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-lg font-semibold"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    Generating 20 Video Scripts...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    Generate 20 Video Scripts
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Streaming Content Display */}
      {isStreaming && rawStreamContent && (
        <div className="mb-8">
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mr-3"></div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">Generating Your Video Scripts...</h2>
                    <p className="text-sm text-gray-600 mt-1">
                      AI is creating 20 personalized scripts based on behavioral science and your product information...
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                  {rawStreamContent}
                </pre>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Generated Scripts Display */}
      {generatedContent?.scripts && generatedContent.scripts.length > 0 && (
        <div className="mb-8">
          <div className="flex items-center mb-6">
            <div className="bg-green-100 p-2 rounded-full mr-3">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">Your Video Scripts</h2>
              <p className="text-gray-600 mb-2">
                {generatedContent.scripts.length} scripts ready for CapCut AI video creation
              </p>
              <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                ✓ Scripts Generated Successfully
              </div>
            </div>
          </div>

          <div className="grid gap-4">
            {generatedContent.scripts.map((script, index) => (
              <div key={script.id || index} className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start">
                      <div className="bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-lg w-10 h-10 flex items-center justify-center text-sm font-bold mr-4">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {script.duration || scriptDuration}
                          </span>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            {script.style || videoStyle}
                          </span>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => navigator.clipboard.writeText(script.script)}
                      className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      Copy
                    </button>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                      {script.script}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">How to Use These Scripts:</h3>
            <ol className="text-sm text-blue-800 space-y-1">
              <li>1. Copy the script you want to use</li>
              <li>2. Open CapCut and create a new project</li>
              <li>3. Use the AI text-to-video feature</li>
              <li>4. Paste the script and generate your video</li>
              <li>5. Customize visuals, music, and effects as needed</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoScriptsPage;
