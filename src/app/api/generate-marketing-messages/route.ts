import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { businessName, targetAudience, businessDescription, messages, generateOneLinerHeadlines } = await request.json();

    if (!businessName || !targetAudience || !businessDescription) {
      return NextResponse.json({ message: 'Business name, target audience and business description are required.' }, { status: 400 });
    }

    const bookContentPath = path.join(process.cwd(), 'public', 'book_summary.md');
    let bookContent = '';
    try {
        bookContent = await fs.readFile(bookContentPath, 'utf-8');
    } catch (readError) {
      console.error('Failed to read book_summary.md:', readError);
      return NextResponse.json({ message: 'Failed to load book content for analysis.' }, { status: 500 });
    }

    const prompt = `
      # Website Headline and Subheadline Generator (Inspired by <PERSON>'s "Jobs to Be Done" Theory)

      You are an expert copywriter and branding strategist, deeply knowledgeable in <PERSON>'s "Jobs to Be Done" (JTBD) theory, as outlined in "Competing Against Luck." Your task is to generate a list of compelling website headlines and subheadlines that resonate with the customer's underlying "Job to Be Done."

      **Reference Principles: Clayton Christensen's "Competing Against Luck"**
      ${bookContent}

      ---

      I will provide you with the Business Name, a description of the Target Audience, and a Business Description. Your output must be distinct, extensive lists.

      ${generateOneLinerHeadlines ? `
      ## 50 One-Liner Headlines:
      *   Each headline must be a single, concise phrase explaining what the business does, derived from the "Jobs to Be Done" theory, focusing on the *solution* or *outcome* for the customer, rather than just a feature.
      *   Follow the style of modern businesses (e.g., Apple, Netflix).
      *   Do not include any punctuation.
      *   Each headline must be at least 6-8 words long.
      *   **Format:** Each headline must be a markdown list item (e.g., "* Headline text").
      *   Example: "* Empower your financial future with intelligent investment strategies"
      ` : ''}

      ## 50 Website Headlines:
      *   Each headline must be a 2-pronged headline, structured as "Problem/Struggle: Solution/Progress."
      *   The **first prong** must articulate the customer's **"struggle" or "push"** (the problem they are trying to overcome, rooted in their current situation and circumstances).
      *   The **second prong** must present the **"progress" or "pull"** (the desired outcome or solution) that the business offers, directly addressing how it helps the customer "hire" the product to get their specific job done. This part should be detailed, compelling, and clearly articulate the transformative outcome or significant progress, using language that is easy to understand, clear, and concise. It must clearly state *what the business does* to provide this solution or progress.
      *   Example: "* Tired of Guessing If Your Meal Is Safe for Diabetes? **Gain Confidence with Instant Nutritional Insights that Simplify Every Meal Choice.**"
      *   Designed to stop the scroll by clearly articulating the job the customer wants to hire a solution for.
      *   **Format:** Each headline must be a markdown list item (e.g., "* Problem: Solution").

      ## 50 Sub-Headlines:
      *   Each sub-headline must be a clear, concise sentence that expands on a headline.
      *   It should explain how the business/product helps the customer "hire" it to get their specific "job to be done" more effectively.
      *   Focus on the functional, social, and emotional dimensions of the job, and how the solution facilitates progress.
      *   Each sub-headline should be easily digestible and avoid jargon.
      *   **Format:** Each sub-headline must be a markdown list item (e.g., "* Sub-headline text").

      ---

      **User's Information:**

      *   **Business Name:** "${businessName}"
      *   **Target Audience:** "${targetAudience}"
      *   **Business Description:** "${businessDescription}"

      ---

      Please generate ONLY the requested headlines and sub-headlines in the specified format, with each list clearly demarcated. Do not include any conversational text, introductions, or conclusions.
    `;

    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.0-flash-exp:free',
        messages: [
          ...(messages || []),
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: true,
      }),
    });

    if (!openRouterResponse.ok) {
      let errorData;
      try {
        errorData = await openRouterResponse.json();
      } catch (jsonError) {
        errorData = await openRouterResponse.text();
      }

      const errorMessage = typeof errorData === 'object' && errorData !== null && 'message' in errorData
        ? errorData.message
        : String(errorData);

      console.error('OpenRouter API error:', errorMessage);
      return NextResponse.json({ message: `OpenRouter API error: ${errorMessage}` }, { status: openRouterResponse.status });
    }

    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        const decoder = new TextDecoder();
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });

            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const jsonStr = line.substring(6);
                if (jsonStr === '[DONE]') {
                  controller.close();
                  return;
                }
                try {
                  const data = JSON.parse(jsonStr);
                  const content = data.choices[0]?.delta?.text || data.choices[0]?.delta?.content || '';
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (parseError) {
                  console.error('Error parsing stream chunk:', parseError);
                  controller.enqueue(new TextEncoder().encode(`[PARSE_ERROR] ${line}\n`));
                }
              } else if (line.trim() !== '') {
                console.warn('Received non-data line in stream:', line);
              }
            }
          }
        } catch (readError) {
          console.error('Error reading stream:', readError);
          controller.error(readError);
        } finally {
          controller.close();
        }
      },
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
      },
    });

  } catch (error: any) {
    console.error('API route error:', error);
    return NextResponse.json({ message: error.message || 'An unexpected error occurred.' }, { status: 500 });
  }
}
