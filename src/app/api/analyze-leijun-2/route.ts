import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { businessIdeal, description, messages } = await request.json();

    if (!businessIdeal || !description) {
      return NextResponse.json({ message: 'Business ideal and description are required.' }, { status: 400 });
    }

    const coreValuesPath = path.join(process.cwd(), '.clinerules', 'workflows', 'extracted_core_values.md');
    let coreValuesContent = '';
    try {
        coreValuesContent = await fs.readFile(coreValuesPath, 'utf-8');
    } catch (readError) {
      console.error('Failed to read extracted_core_values.md:', readError);
      return NextResponse.json({ message: 'Failed to load core values for analysis.' }, { status: 500 });
    }

    const prompt = `
      You are a world-class business strategist and <PERSON><PERSON> methodology expert, deeply versed in the principles that built <PERSON>mi into a global powerhouse. Your mission is to help this business apply <PERSON><PERSON>'s proven methodology to achieve explosive growth and market success.

      **Core Reference: <PERSON><PERSON>'s Xiaomi Methodology**
      ${coreValuesContent}

      ---

      **Business to Analyze:**
      - **Business/Product:** "${businessIdeal}"
      - **Description:** "${description}"

      ---

      **Your Task: Deep Lei Jun Methodology Analysis**

      Provide a comprehensive, actionable analysis using Lei Jun's proven framework. Structure your response in well-formatted markdown with the following sections:

      ## 1. 🎯 Seven-Character Mantra Analysis (专注、极致、口碑、快)

      **专注 (Focus) Assessment:**
      - Evaluate how focused this business concept is
      - Identify the core focus area and potential distractions
      - Recommend specific focus strategies

      **极致 (Extremity) Potential:**
      - Assess the potential for achieving "ultimate" user experience
      - Identify areas where the product can exceed expectations
      - Define what "extremity" means for this specific business

      **口碑 (Word-of-Mouth) Strategy:**
      - Analyze the viral potential and shareability
      - Identify what would make users passionate advocates
      - Design word-of-mouth amplification tactics

      **快 (Fast) Implementation:**
      - Evaluate speed-to-market opportunities
      - Recommend rapid iteration strategies
      - Identify areas for agile development

      ## 2. ⚡ Iron Triangle Integration (Software + Hardware + Internet Services)

      **Current Integration Level:**
      - Assess how this business integrates software, hardware, and services
      - Identify missing components of the triangle
      - Evaluate synergy potential

      **Enhancement Opportunities:**
      - Specific recommendations for each triangle component
      - How to create unique competitive advantages through integration
      - Platform and ecosystem building strategies

      ## 3. 💥 Explosive Product (爆品) Potential Analysis

      **爆品 Readiness Score (1-10):**
      - Evaluate the potential for creating a market-disrupting product
      - Assess "感动人心，价格厚道" (Touching Hearts, Honest Prices) alignment

      **Path to 爆品 Status:**
      - Specific product improvements needed
      - Pricing strategy recommendations
      - Market positioning for explosive growth

      **Supply Chain & Scalability:**
      - Requirements for handling explosive demand
      - Operational efficiency recommendations

      ## 4. 🌐 Ecosystem Building Strategy

      **Core Ecosystem Vision:**
      - Design a comprehensive ecosystem around this business
      - Identify 5-7 complementary products/services
      - Map user journey across the ecosystem

      **Partnership & Investment Strategy:**
      - Potential strategic partnerships
      - "小而美" (Small but Beautiful) team opportunities
      - Ecosystem expansion roadmap

      **Network Effects & Lock-in:**
      - How to create user stickiness
      - Data and platform advantages
      - Competitive moats through ecosystem

      ## 5. 💰 Efficiency & Cost-Effectiveness (性价比) Analysis

      **Current Efficiency Assessment:**
      - Evaluate operational efficiency potential
      - Identify cost reduction opportunities
      - Assess direct-to-consumer (D2C) viability

      **Lei Jun's 5% Hardware Profit Model:**
      - How this principle applies to your business
      - Revenue diversification strategies
      - Sustainable profitability model

      **Marketing Efficiency:**
      - Reduce traditional marketing through product excellence
      - Community-driven growth strategies
      - Organic user acquisition tactics

      ## 6. 👥 User Community & "Mi Fan" Strategy

      **Community Building Potential:**
      - Assess the potential for passionate user communities
      - Design user co-creation opportunities
      - Forum and feedback integration strategies

      **User-Centric Development:**
      - How to make users true partners in development
      - Transparency and trust-building tactics
      - "感动人心" (Touching Hearts) implementation

      **Fan Economy Activation:**
      - Transform customers into advocates
      - User-generated content strategies
      - Community-driven marketing

      ## 7. 🏭 Smart Manufacturing & Vertical Integration

      **Manufacturing Strategy:**
      - Assess needs for smart manufacturing
      - Quality control and innovation opportunities
      - Vertical integration recommendations

      **Technology Foundation:**
      - Core technology development priorities
      - "硬核科技" (Hardcore Technology) opportunities
      - R&D investment strategy

      ## 8. 🚀 Implementation Roadmap

      **Phase 1: Foundation (0-6 months)**
      - Immediate focus and product refinement
      - Core team building
      - Initial user community establishment

      **Phase 2: Growth (6-18 months)**
      - Product iteration and improvement
      - Ecosystem expansion
      - Market penetration strategies

      **Phase 3: Scale (18+ months)**
      - Full ecosystem deployment
      - International expansion
      - Platform and partnership development

      ## 9. 🎯 Success Metrics & KPIs

      **Lei Jun-Style Metrics:**
      - User satisfaction and NPS scores
      - Word-of-mouth amplification rates
      - Ecosystem engagement metrics
      - Operational efficiency indicators

      **Milestone Targets:**
      - Specific, measurable goals for each phase
      - Community growth targets
      - Revenue and market share objectives

      ---

      **Remember:** Focus on practical, actionable insights that can be immediately implemented. Every recommendation should be grounded in Lei Jun's proven principles and adapted specifically to this business context. Be specific, bold, and transformative in your suggestions.
    `;

    // Call the OpenRouter API
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App - Lei Jun Analysis',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.0-flash-exp:free',
        messages: [
          ...(messages || []),
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: true,
      }),
    });

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.text();
      console.error('OpenRouter API error:', errorData);
      return NextResponse.json({ message: 'Failed to get response from AI service.' }, { status: 500 });
    }

    // Create a readable stream to process the response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        const decoder = new TextDecoder();
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  controller.close();
                  return;
                }
                
                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (e) {
                  // Skip invalid JSON
                  continue;
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream processing error:', error);
        } finally {
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error in analyze-leijun-2 API:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
