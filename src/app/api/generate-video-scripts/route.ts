import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const { videoAnalysis, productName, productDescription, targetAudience, callToAction, videoStyle, scriptType, scriptDuration } = await request.json();

    if (!productName || !productDescription || !targetAudience || !callToAction || !videoStyle || !scriptType || !scriptDuration) {
      return NextResponse.json({ message: 'Missing required fields.' }, { status: 400 });
    }

    // Build behavioral science context from video analysis if available
    let behavioralScienceContext = '';
    if (videoAnalysis && videoAnalysis.principles && videoAnalysis.principles.length > 0) {
      behavioralScienceContext = `
BEHAVIORAL SCIENCE INSIGHTS FROM VIRAL VIDEO ANALYSIS:
${videoAnalysis.principles.map((principle: any, index: number) => `
${index + 1}. ${principle.name}
   - Explanation: ${principle.explanation}
   - Video Example: ${principle.videoExample}
`).join('')}

Use these psychological principles to enhance the scripts' effectiveness.
`;
    } else {
      behavioralScienceContext = `
BEHAVIORAL SCIENCE PRINCIPLES TO APPLY:
1. Hook Principle - Start with attention-grabbing opening (curiosity, surprise, controversy)
2. Social Proof - Include testimonials, user numbers, or social validation
3. Scarcity/Urgency - Create time-sensitive or limited availability messaging
4. Problem-Solution Framework - Identify pain points and present clear solutions
5. Emotional Triggers - Use fear, joy, surprise, or aspiration to connect
6. Authority - Establish credibility through expertise or endorsements
7. Reciprocity - Offer value before asking for action
`;
    }

    const prompt = `You are an elite video script writer specializing in viral content creation for CapCut AI video generation. Create 20 pure scripts (no scenes, dialogue, or actions - just narrative text) that can be directly input into CapCut's AI video creation feature.

${behavioralScienceContext}

PRODUCT INFORMATION:
- Product/Business: ${productName}
- Description: ${productDescription}
- Target Audience: ${targetAudience}
- Call to Action: ${callToAction}

SCRIPT SPECIFICATIONS:
- Video Style: ${videoStyle}
- Script Type: ${scriptType}
- Duration: ${scriptDuration}

CRITICAL REQUIREMENTS:
1. Generate exactly 20 scripts
2. Each script should be pure narrative text suitable for CapCut AI
3. No scene descriptions, dialogue, or action directions
4. Scripts should be ${scriptDuration} worth of spoken content
5. Apply behavioral science principles to maximize engagement
6. Tailor content to ${videoStyle} style and ${scriptType} approach
7. Include strong hooks, clear value propositions, and compelling CTAs
8. Vary the scripts to provide different angles and approaches

SCRIPT STYLE GUIDELINES:
- UGC: Conversational, authentic, personal experience tone
- Personal Perspective: First-person narrative, "I" statements, personal journey
- Explainer: Clear, educational, step-by-step information delivery
- Testimonial: Customer success stories, before/after transformations
- Educational: Informative, how-to, teaching-focused content
- Promotional: Sales-focused, benefit-driven, persuasive messaging
- Storytelling: Narrative structure with beginning, middle, end

SCRIPT TYPE FOCUS:
- Hook-focused: Start with powerful attention-grabbers
- Problem-solution: Identify pain points then present solutions
- Feature-benefit: Highlight product features and their benefits
- Social proof: Emphasize testimonials, reviews, user success
- Curiosity-driven: Create intrigue and desire to learn more
- Emotional: Connect through feelings, aspirations, fears
- Comparison: Show before/after or vs. competitors

OUTPUT FORMAT:
Respond with valid JSON only:
{
  "scripts": [
    {
      "id": 1,
      "script": "Your first script text here...",
      "duration": "${scriptDuration}",
      "style": "${videoStyle}"
    },
    {
      "id": 2,
      "script": "Your second script text here...",
      "duration": "${scriptDuration}",
      "style": "${videoStyle}"
    }
    // ... continue for all 20 scripts
  ]
}

Each script should be engaging, actionable, and optimized for ${scriptDuration} video content that drives ${callToAction}.`;

    console.log('Sending request to OpenRouter for video scripts generation...');

    // Make request to OpenRouter
    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App - Video Scripts',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-flash',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        stream: true,
        temperature: 0.8 // Add creativity while maintaining structure
      }),
    });

    if (!openRouterResponse.ok) {
      const errorText = await openRouterResponse.text();
      console.error('OpenRouter API error:', errorText);
      return NextResponse.json({ message: `OpenRouter API error: ${errorText}` }, { status: openRouterResponse.status });
    }

    // Create a readable stream to handle the response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.error(new Error('No response body'));
          return;
        }

        try {
          let buffer = '';

          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = new TextDecoder().decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    buffer += content;
                    // Send only the new content chunk
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (parseError) {
                  // Skip invalid JSON chunks
                  continue;
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream processing error:', error);
          const errorMessage = `Stream error: ${error instanceof Error ? error.message : 'Unknown error'}`;
          controller.enqueue(new TextEncoder().encode(errorMessage));
          controller.error(error);
        } catch (error) {
          console.error('Stream processing error:', error);
          const errorMessage = `Stream error: ${error instanceof Error ? error.message : 'Unknown error'}`;
          controller.enqueue(new TextEncoder().encode(errorMessage));
          controller.error(error);
        } finally {
          console.log('Stream processing finished');
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error in generate-video-scripts API:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
