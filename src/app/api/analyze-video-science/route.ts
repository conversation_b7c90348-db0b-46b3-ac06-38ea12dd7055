import { NextResponse } from 'next/server';
import { downloadVideoFromUrl, getVideoMetadata, identifyPlatform, isSupportedPlatform, validateVideoForGemini } from '@/lib/videoUrlProcessor';

export async function POST(request: Request) {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

    if (!GEMINI_API_KEY) {
      return NextResponse.json({ message: 'Gemini API key is not configured.' }, { status: 500 });
    }

    const formData = await request.formData();
    const videoUrl = formData.get('videoUrl') as string;
    const videoFile = formData.get('video') as File;

    if (!videoUrl && !videoFile) {
      return NextResponse.json({ message: 'Either video URL or video file is required.' }, { status: 400 });
    }

    let videoAnalysis: any = null;

    if (videoFile) {
      // Handle uploaded video file
      const maxSize = 20 * 1024 * 1024; // 20MB limit for Gemini
      if (videoFile.size > maxSize) {
        return NextResponse.json({ message: 'Video file is too large. Maximum size is 20MB.' }, { status: 400 });
      }

      try {
        const arrayBuffer = await videoFile.arrayBuffer();
        const base64 = Buffer.from(arrayBuffer).toString('base64');

        const geminiResponse = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent', {
          method: 'POST',
          headers: {
            'x-goog-api-key': GEMINI_API_KEY,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    inline_data: {
                      mime_type: videoFile.type,
                      data: base64
                    }
                  },
                  {
                    text: `Analyze this viral video based on the specific behavioral science anatomy that makes content go viral.

CRITICAL: Respond ONLY with valid JSON. No other text.

Analyze the video for these 7 key viral anatomy elements:

1. HOOK - How does it grab attention in the first 3 seconds?
2. EMOTIONAL CONNECTION - What emotions does it trigger (joy, surprise, fear, anger, etc.)?
3. RELATABILITY & IDENTITY EXPRESSION - How does it make viewers feel seen/understood?
4. CONCISE AND ENGAGING STORYTELLING - How does it tell a story quickly and effectively?
5. SHAREABILITY - What makes people want to share this content?
6. TIMELINESS - How does it tap into current trends, events, or cultural moments?
7. AUTHENTICITY - What makes it feel genuine and trustworthy?

For each element found, provide: name, explanation, and specific video example.

JSON format (copy exactly):
{
  "viralAnatomy": [
    {
      "name": "Hook",
      "explanation": "How the video grabs attention in the opening seconds",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Emotional Connection",
      "explanation": "What emotions are triggered and how",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Relatability & Identity Expression",
      "explanation": "How viewers can relate or express identity",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Concise and Engaging Storytelling",
      "explanation": "How the story is told effectively",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Shareability",
      "explanation": "What makes it shareable",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Timeliness",
      "explanation": "How it connects to current trends/moments",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Authenticity",
      "explanation": "What makes it feel genuine",
      "videoExample": "Specific example from this video"
    }
  ]
}`
                  }
                ]
              }
            ]
          }),
        });

        if (!geminiResponse.ok) {
          const errorData = await geminiResponse.text();
          console.error('Gemini API error:', errorData);
          return NextResponse.json({ message: 'Failed to analyze video with Gemini API.' }, { status: 500 });
        }

        const geminiData = await geminiResponse.json();
        const analysisText = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!analysisText) {
          return NextResponse.json({ message: 'No analysis received from Gemini API.' }, { status: 500 });
        }

        console.log('Raw Gemini response:', analysisText);

        // Parse the JSON response from Gemini
        try {
          let jsonString = '';

          // First, try to extract JSON from markdown code blocks
          const codeBlockMatch = analysisText.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
          if (codeBlockMatch) {
            jsonString = codeBlockMatch[1];
            console.log('Extracted JSON from code block:', jsonString);
          } else {
            // Fallback: Try to find JSON by looking for the first { and last }
            const firstBrace = analysisText.indexOf('{');
            const lastBrace = analysisText.lastIndexOf('}');

            if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
              jsonString = analysisText.substring(firstBrace, lastBrace + 1);
              console.log('Extracted JSON string:', jsonString);
            } else {
              throw new Error('No JSON found in response');
            }
          }

          // Parse the JSON
          videoAnalysis = JSON.parse(jsonString);

          // Validate and fix the structure
          if (!videoAnalysis.viralAnatomy) {
            console.warn('Missing viralAnatomy array in videoAnalysis, initializing empty array');
            videoAnalysis.viralAnatomy = [];
          } else if (!Array.isArray(videoAnalysis.viralAnatomy)) {
            console.warn('viralAnatomy is not an array, converting to array');
            videoAnalysis.viralAnatomy = [videoAnalysis.viralAnatomy];
          }

          // Add fileName if missing
          if (!videoAnalysis.fileName) {
            videoAnalysis.fileName = videoFile.name;
          }

          console.log('Successfully parsed video analysis with', videoAnalysis.viralAnatomy.length, 'viral anatomy elements');

        } catch (parseError) {
          console.error('Failed to parse Gemini response:', parseError);
          console.error('Raw response text:', analysisText);
          // Create a fallback response
          videoAnalysis = {
            fileName: videoFile.name,
            viralAnatomy: []
          };
        }

      } catch (error) {
        console.error('Error processing video file:', error);
        return NextResponse.json({ message: 'Failed to process video file.' }, { status: 500 });
      }

    } else if (videoUrl) {
      // Handle video URL
      try {
        // Validate URL and platform
        if (!isSupportedPlatform(videoUrl)) {
          const platform = identifyPlatform(videoUrl);
          return NextResponse.json({
            message: `Unsupported platform: ${platform}. Supported platforms: TikTok, Instagram, YouTube, Twitter/X, Facebook.`
          }, { status: 400 });
        }

        // Get basic metadata first
        const metadata = await getVideoMetadata(videoUrl);
        if (!metadata.isValid) {
          return NextResponse.json({
            message: 'Invalid video URL. Please check the URL and try again.'
          }, { status: 400 });
        }

        // Attempt to download and process the video
        try {
          console.log(`Attempting to download video from ${metadata.platform}: ${videoUrl}`);
          const videoInfo = await downloadVideoFromUrl(videoUrl);

          // Validate the downloaded video
          const validation = validateVideoForGemini(videoInfo.buffer, videoInfo.mimeType);
          if (!validation.isValid) {
            return NextResponse.json({
              message: validation.error || 'Downloaded video is not compatible with analysis.'
            }, { status: 400 });
          }

          // Process the downloaded video with Gemini
          const base64 = videoInfo.buffer.toString('base64');
          console.log(`Video downloaded successfully, size: ${(videoInfo.buffer.length / 1024 / 1024).toFixed(1)}MB`);

          const geminiResponse = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent', {
            method: 'POST',
            headers: {
              'x-goog-api-key': GEMINI_API_KEY,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contents: [
                {
                  parts: [
                    {
                      inline_data: {
                        mime_type: videoInfo.mimeType,
                        data: base64
                      }
                    },
                    {
                      text: `Analyze this ${videoInfo.platform} viral video based on the specific behavioral science anatomy that makes content go viral.

CRITICAL: Respond ONLY with valid JSON. No other text.

Analyze the video for these 7 key viral anatomy elements:

1. HOOK - How does it grab attention in the first 3 seconds?
2. EMOTIONAL CONNECTION - What emotions does it trigger (joy, surprise, fear, anger, etc.)?
3. RELATABILITY & IDENTITY EXPRESSION - How does it make viewers feel seen/understood?
4. CONCISE AND ENGAGING STORYTELLING - How does it tell a story quickly and effectively?
5. SHAREABILITY - What makes people want to share this content?
6. TIMELINESS - How does it tap into current trends, events, or cultural moments?
7. AUTHENTICITY - What makes it feel genuine and trustworthy?

For each element found, provide: name, explanation, and specific video example.

JSON format (copy exactly):
{
  "viralAnatomy": [
    {
      "name": "Hook",
      "explanation": "How the video grabs attention in the opening seconds",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Emotional Connection",
      "explanation": "What emotions are triggered and how",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Relatability & Identity Expression",
      "explanation": "How viewers can relate or express identity",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Concise and Engaging Storytelling",
      "explanation": "How the story is told effectively",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Shareability",
      "explanation": "What makes it shareable",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Timeliness",
      "explanation": "How it connects to current trends/moments",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Authenticity",
      "explanation": "What makes it feel genuine",
      "videoExample": "Specific example from this video"
    }
  ]
}`
                    }
                  ]
                }
              ]
            }),
          });

          if (!geminiResponse.ok) {
            const errorData = await geminiResponse.text();
            console.error('Gemini API error:', errorData);
            return NextResponse.json({ message: 'Failed to analyze video with Gemini API.' }, { status: 500 });
          }

          const geminiData = await geminiResponse.json();
          const analysisText = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;

          if (!analysisText) {
            return NextResponse.json({ message: 'No analysis received from Gemini API.' }, { status: 500 });
          }

          console.log('Raw Gemini response for URL video:', analysisText);

          // Parse the JSON response from Gemini
          try {
            let jsonString = '';

            // First, try to extract JSON from markdown code blocks
            const codeBlockMatch = analysisText.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
            if (codeBlockMatch) {
              jsonString = codeBlockMatch[1];
              console.log('Extracted JSON from code block:', jsonString);
            } else {
              // Fallback: Try to find JSON by looking for the first { and last }
              const firstBrace = analysisText.indexOf('{');
              const lastBrace = analysisText.lastIndexOf('}');

              if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
                jsonString = analysisText.substring(firstBrace, lastBrace + 1);
                console.log('Extracted JSON string:', jsonString);
              } else {
                throw new Error('No JSON found in response');
              }
            }

            // Parse the JSON
            videoAnalysis = JSON.parse(jsonString);

            // Validate and fix the structure
            if (!videoAnalysis.principles) {
              console.warn('Missing principles array in videoAnalysis, initializing empty array');
              videoAnalysis.principles = [];
            } else if (!Array.isArray(videoAnalysis.principles)) {
              console.warn('principles is not an array, converting to array');
              videoAnalysis.principles = [videoAnalysis.principles];
            }

            // Add missing fields
            if (!videoAnalysis.videoUrl) {
              videoAnalysis.videoUrl = videoUrl;
            }
            if (!videoAnalysis.platform) {
              videoAnalysis.platform = videoInfo.platform;
            }

            console.log('Successfully parsed video analysis with', videoAnalysis.principles.length, 'principles');

          } catch (parseError) {
            console.error('Failed to parse Gemini response:', parseError);
            console.error('Raw response text:', analysisText);
            // Create a fallback response
            videoAnalysis = {
              videoUrl: videoUrl,
              platform: videoInfo.platform,
              principles: []
            };
          }

        } catch (downloadError) {
          // If video download fails, return a helpful error message
          const platform = identifyPlatform(videoUrl);
          console.error(`Video download error for ${platform}:`, downloadError);

          return NextResponse.json({
            message: `Unable to process video from ${platform}. ${downloadError instanceof Error ? downloadError.message : 'Unknown error occurred'}`,
            suggestion: 'Please try uploading the video file directly instead.',
            platform: platform
          }, { status: 400 });
        }

      } catch (error) {
        console.error('Error processing video URL:', error);
        return NextResponse.json({ message: 'Failed to process video URL.' }, { status: 500 });
      }
    }

    // Validate the videoAnalysis object before returning
    if (!videoAnalysis || typeof videoAnalysis !== 'object') {
      console.error('Invalid videoAnalysis object:', videoAnalysis);
      return NextResponse.json({
        message: 'Failed to generate valid video analysis.'
      }, { status: 500 });
    }

    // Ensure the principles array exists
    if (!videoAnalysis.principles || !Array.isArray(videoAnalysis.principles)) {
      console.warn('Missing principles array in videoAnalysis, initializing empty array');
      videoAnalysis.principles = [];
    }

    return NextResponse.json({ videoAnalysis });

  } catch (error) {
    console.error('Error in analyze-video-science API:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
